
import React, { useCallback } from 'react';
import { motion } from 'framer-motion';
import { Upload, Film, FileVideo, AlertCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

const FileUpload = ({ onFileSelect, className }) => {
  const validateVideoFile = (file) => {
    const validTypes = [
      'video/mp4',
      'video/webm',
      'video/ogg',
      'video/quicktime',
      'video/x-matroska',
      'video/x-msvideo',
      'video/x-ms-wmv'
    ];

    if (!validTypes.includes(file.type)) {
      toast({
        title: "صيغة غير مدعومة",
        description: "يرجى اختيار ملف فيديو بصيغة مدعومة",
        variant: "destructive"
      });
      return false;
    }

    // التحقق من حجم الملف (الحد الأقصى 500 ميجابايت)
    const maxSize = 500 * 1024 * 1024; // 500MB
    if (file.size > maxSize) {
      toast({
        title: "حجم الملف كبير جداً",
        description: "يجب أن لا يتجاوز حجم الملف 500 ميجابايت",
        variant: "destructive"
      });
      return false;
    }

    return true;
  };

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const videoFiles = files.filter(file => file.type.startsWith('video/'));
    
    if (videoFiles.length === 0) {
      toast({
        title: "خطأ",
        description: "يرجى اختيار ملفات فيديو صالحة",
        variant: "destructive"
      });
      return;
    }

    videoFiles.forEach(file => {
      if (validateVideoFile(file)) {
        // إنشاء URL للفيديو وفحص صلاحيته
        const videoUrl = URL.createObjectURL(file);
        const video = document.createElement('video');
        video.src = videoUrl;

        video.onloadedmetadata = () => {
          onFileSelect(file, video.duration);
        };

        video.onerror = () => {
          URL.revokeObjectURL(videoUrl);
          toast({
            title: "خطأ في الملف",
            description: "لا يمكن تشغيل ملف الفيديو المحدد",
            variant: "destructive"
          });
        };
      }
    });
  }, [onFileSelect]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.add('border-primary');
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    e.currentTarget.classList.remove('border-primary');
  }, []);

  const handleFileInput = (e) => {
    const files = Array.from(e.target.files);
    files.forEach(file => {
      if (validateVideoFile(file)) {
        const videoUrl = URL.createObjectURL(file);
        const video = document.createElement('video');
        video.src = videoUrl;

        video.onloadedmetadata = () => {
          onFileSelect(file, video.duration);
        };

        video.onerror = () => {
          URL.revokeObjectURL(videoUrl);
          toast({
            title: "خطأ في الملف",
            description: "لا يمكن تشغيل ملف الفيديو المحدد",
            variant: "destructive"
          });
        };
      }
    });
    // إعادة تعيين قيمة حقل الإدخال للسماح برفع نفس الملف مرة أخرى
    e.target.value = '';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`file-upload-area rounded-xl p-8 text-center ${className}`}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <motion.div
        whileHover={{ scale: 1.05 }}
        className="flex flex-col items-center gap-4"
      >
        <div className="relative">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 opacity-20"
          />
          <div className="relative bg-white/10 backdrop-blur-sm rounded-full p-6">
            <Upload className="w-12 h-12 text-white" />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-xl font-bold text-white">ارفع ملفات الفيديو</h3>
          <p className="text-gray-300">اسحب وأفلت الملفات هنا أو انقر للاختيار</p>
        </div>

        <div className="flex gap-4 text-sm text-gray-400">
          <div className="flex items-center gap-1">
            <Film className="w-4 h-4" />
            <span>MP4</span>
          </div>
          <div className="flex items-center gap-1">
            <FileVideo className="w-4 h-4" />
            <span>WebM</span>
          </div>
          <div className="flex items-center gap-1">
            <FileVideo className="w-4 h-4" />
            <span>MOV</span>
          </div>
          <div className="flex items-center gap-1">
            <FileVideo className="w-4 h-4" />
            <span>MKV</span>
          </div>
        </div>

        <div className="text-xs text-gray-400 flex items-center gap-1">
          <AlertCircle className="w-3 h-3" />
          <span>الحد الأقصى لحجم الملف: 500 ميجابايت</span>
        </div>

        <input
          type="file"
          multiple
          accept="video/*"
          onChange={handleFileInput}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
      </motion.div>
    </motion.div>
  );
};

export default FileUpload;