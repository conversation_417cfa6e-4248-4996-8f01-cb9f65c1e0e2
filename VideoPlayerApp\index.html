<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشغل الفيديو المتقدم</title>
    <meta name="description" content="مشغل فيديو متقدم يدعم جميع صيغ الفيديو">
    <meta name="theme-color" content="#1e1b4b">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="./icon-192.png">
    <link rel="apple-touch-icon" href="./icon-192.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1b4b, #312e81, #1e40af);
            color: white;
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #60a5fa, #a78bfa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        .video-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }
        
        .upload-area {
            border: 3px dashed rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 50px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .upload-area:hover {
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.05);
        }
        
        .upload-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.7;
        }
        
        .upload-text {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .upload-subtext {
            opacity: 0.7;
            margin-bottom: 20px;
        }
        
        .file-input {
            display: none;
        }
        
        .video-player {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .video-player video {
            width: 100%;
            height: auto;
        }
        
        .playlist {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .playlist h3 {
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .playlist-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .playlist-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .playlist-item.active {
            background: rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.5);
        }
        
        .playlist-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #8b5cf6, #3b82f6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .playlist-info {
            flex: 1;
        }
        
        .playlist-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .playlist-meta {
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .remove-btn {
            background: rgba(239, 68, 68, 0.2);
            border: none;
            color: #ef4444;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .remove-btn:hover {
            background: rgba(239, 68, 68, 0.4);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            background: linear-gradient(45deg, #8b5cf6, #3b82f6);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .video-container {
                padding: 20px;
            }
            
            .upload-area {
                padding: 30px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎥 مشغل الفيديو المتقدم</h1>
            <p>يدعم جميع صيغ الفيديو مع واجهة عصرية وسهلة الاستخدام</p>
        </header>
        
        <div class="video-container">
            <div id="uploadArea" class="upload-area">
                <div class="upload-icon">📁</div>
                <div class="upload-text">اختر ملفات الفيديو</div>
                <div class="upload-subtext">اضغط هنا أو اسحب الملفات لإضافتها</div>
                <div class="upload-subtext">الصيغ المدعومة: MP4, WebM, OGG, MOV, AVI</div>
                <input type="file" id="fileInput" class="file-input" multiple accept="video/*">
            </div>
            
            <div id="videoPlayer" class="video-player hidden">
                <video id="video" controls>
                    متصفحك لا يدعم تشغيل الفيديو
                </video>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="selectFiles()">إضافة ملفات</button>
                <button class="btn" onclick="clearPlaylist()">مسح القائمة</button>
            </div>
        </div>
        
        <div class="playlist">
            <h3>📋 قائمة التشغيل (<span id="videoCount">0</span>)</h3>
            <div id="playlistContainer">
                <div style="text-align: center; opacity: 0.7; padding: 40px;">
                    لا توجد فيديوهات في القائمة
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let videos = [];
        let currentVideoIndex = -1;
        
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const videoPlayer = document.getElementById('videoPlayer');
        const video = document.getElementById('video');
        const playlistContainer = document.getElementById('playlistContainer');
        const videoCount = document.getElementById('videoCount');
        
        // تحميل البيانات المحفوظة
        loadSavedData();
        
        // إعداد الأحداث
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileSelect);
        video.addEventListener('ended', playNext);
        
        // السحب والإفلات
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.8)';
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            handleFiles(e.dataTransfer.files);
        });
        
        function handleFileSelect(e) {
            handleFiles(e.target.files);
        }
        
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (file.type.startsWith('video/')) {
                    const videoData = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        url: URL.createObjectURL(file),
                        size: file.size,
                        type: file.type
                    };
                    
                    videos.push(videoData);
                }
            });
            
            updatePlaylist();
            saveData();
            
            if (currentVideoIndex === -1 && videos.length > 0) {
                playVideo(0);
            }
        }
        
        function updatePlaylist() {
            videoCount.textContent = videos.length;
            
            if (videos.length === 0) {
                playlistContainer.innerHTML = '<div style="text-align: center; opacity: 0.7; padding: 40px;">لا توجد فيديوهات في القائمة</div>';
                return;
            }
            
            playlistContainer.innerHTML = videos.map((video, index) => `
                <div class="playlist-item ${index === currentVideoIndex ? 'active' : ''}" onclick="playVideo(${index})">
                    <div class="playlist-icon">▶️</div>
                    <div class="playlist-info">
                        <div class="playlist-name">${video.name}</div>
                        <div class="playlist-meta">${formatFileSize(video.size)} • ${video.type}</div>
                    </div>
                    <button class="remove-btn" onclick="removeVideo(${index}, event)">🗑️</button>
                </div>
            `).join('');
        }
        
        function playVideo(index) {
            if (index < 0 || index >= videos.length) return;
            
            currentVideoIndex = index;
            const videoData = videos[index];
            
            video.src = videoData.url;
            video.load();
            
            uploadArea.classList.add('hidden');
            videoPlayer.classList.remove('hidden');
            
            updatePlaylist();
            saveData();
        }
        
        function playNext() {
            if (currentVideoIndex < videos.length - 1) {
                playVideo(currentVideoIndex + 1);
            }
        }
        
        function removeVideo(index, event) {
            event.stopPropagation();
            
            if (confirm('هل تريد حذف هذا الفيديو من القائمة؟')) {
                URL.revokeObjectURL(videos[index].url);
                videos.splice(index, 1);
                
                if (index === currentVideoIndex) {
                    if (videos.length > 0) {
                        const newIndex = Math.min(index, videos.length - 1);
                        playVideo(newIndex);
                    } else {
                        currentVideoIndex = -1;
                        videoPlayer.classList.add('hidden');
                        uploadArea.classList.remove('hidden');
                    }
                } else if (index < currentVideoIndex) {
                    currentVideoIndex--;
                }
                
                updatePlaylist();
                saveData();
            }
        }
        
        function selectFiles() {
            fileInput.click();
        }
        
        function clearPlaylist() {
            if (videos.length === 0) return;
            
            if (confirm('هل تريد مسح جميع الفيديوهات من القائمة؟')) {
                videos.forEach(video => URL.revokeObjectURL(video.url));
                videos = [];
                currentVideoIndex = -1;
                
                videoPlayer.classList.add('hidden');
                uploadArea.classList.remove('hidden');
                
                updatePlaylist();
                saveData();
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function saveData() {
            try {
                const dataToSave = {
                    currentVideoIndex,
                    videosCount: videos.length
                };
                localStorage.setItem('videoPlayerData', JSON.stringify(dataToSave));
            } catch (error) {
                console.error('Error saving data:', error);
            }
        }
        
        function loadSavedData() {
            try {
                const savedData = localStorage.getItem('videoPlayerData');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    // يمكن استعادة بعض الإعدادات هنا
                }
            } catch (error) {
                console.error('Error loading data:', error);
            }
        }
        
        // تسجيل Service Worker للـ PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }
    </script>
</body>
</html>