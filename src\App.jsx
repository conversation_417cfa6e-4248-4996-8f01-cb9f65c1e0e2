import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import VideoPlayer from '@/components/VideoPlayer';
import FileUpload from '@/components/FileUpload';
import Playlist from '@/components/Playlist';
import { Toaster } from '@/components/ui/toaster';
import { toast } from '@/components/ui/use-toast';
import { Play, List, Sparkles } from 'lucide-react';

function App() {
  const [videos, setVideos] = useState([]);
  const [currentVideo, setCurrentVideo] = useState(null);
  const [showPlaylist, setShowPlaylist] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // تحميل الفيديوهات من localStorage عند تحميل التطبيق
  useEffect(() => {
    const loadSavedVideos = () => {
      try {
        const savedVideos = localStorage.getItem('videoPlayerVideos');
        if (savedVideos) {
          const parsedVideos = JSON.parse(savedVideos);
          setVideos(parsedVideos);
          
          if (parsedVideos.length > 0) {
            // تحقق من صلاحية URL الفيديو قبل تعيينه
            const lastVideo = parsedVideos[0];
            if (lastVideo && lastVideo.url) {
              setCurrentVideo(lastVideo);
            }
          }
        }
      } catch (error) {
        console.error('Error loading saved videos:', error);
        toast({
          title: "خطأ في تحميل الفيديوهات",
          description: "حدث خطأ أثناء تحميل الفيديوهات المحفوظة",
          variant: "destructive"
        });
      }
    };

    loadSavedVideos();
  }, []);

  // حفظ الفيديوهات في localStorage مع تأخير
  useEffect(() => {
    const saveTimeout = setTimeout(() => {
      try {
        // حفظ البيانات الأساسية فقط للفيديوهات
        const videosToSave = videos.map(video => ({
          id: video.id,
          name: video.name,
          url: video.url,
          size: video.size,
          type: video.type,
          duration: video.duration
        }));
        localStorage.setItem('videoPlayerVideos', JSON.stringify(videosToSave));
      } catch (error) {
        console.error('Error saving videos:', error);
        toast({
          title: "خطأ في حفظ الفيديوهات",
          description: "حدث خطأ أثناء حفظ الفيديوهات",
          variant: "destructive"
        });
      }
    }, 1000);

    return () => clearTimeout(saveTimeout);
  }, [videos]);

  const handleFileSelect = useCallback((file, duration) => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      const videoUrl = URL.createObjectURL(file);
      const newVideo = {
        id: Date.now().toString(),
        name: file.name,
        url: videoUrl,
        size: file.size,
        type: file.type,
        duration: duration || 0
      };

      setVideos(prev => [newVideo, ...prev]);
      
      if (!currentVideo) {
        setCurrentVideo(newVideo);
      }

      toast({
        title: "تم إضافة الفيديو",
        description: `تم إضافة ${file.name} إلى قائمة التشغيل`,
      });
    } catch (error) {
      console.error('Error adding video:', error);
      toast({
        title: "خطأ في إضافة الفيديو",
        description: "حدث خطأ أثناء إضافة الفيديو",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentVideo, isLoading]);

  const handleVideoSelect = useCallback((video) => {
    if (isLoading || currentVideo?.id === video.id) return;

    try {
      setIsLoading(true);
      setCurrentVideo(video);
    } catch (error) {
      console.error('Error selecting video:', error);
      toast({
        title: "خطأ في تشغيل الفيديو",
        description: "حدث خطأ أثناء محاولة تشغيل الفيديو",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentVideo, isLoading]);

  const handleVideoRemove = useCallback((videoId) => {
    try {
      const videoToRemove = videos.find(v => v.id === videoId);
      if (videoToRemove) {
        URL.revokeObjectURL(videoToRemove.url);
      }

      setVideos(prev => prev.filter(v => v.id !== videoId));
      
      if (currentVideo?.id === videoId) {
        const remainingVideos = videos.filter(v => v.id !== videoId);
        setCurrentVideo(remainingVideos.length > 0 ? remainingVideos[0] : null);
      }

      toast({
        title: "تم حذف الفيديو",
        description: "تم حذف الفيديو من قائمة التشغيل",
      });
    } catch (error) {
      console.error('Error removing video:', error);
      toast({
        title: "خطأ في حذف الفيديو",
        description: "حدث خطأ أثناء محاولة حذف الفيديو",
        variant: "destructive"
      });
    }
  }, [videos, currentVideo]);

  const handleVideoEnded = useCallback(() => {
    try {
      const currentIndex = videos.findIndex(v => v.id === currentVideo?.id);
      if (currentIndex < videos.length - 1) {
        handleVideoSelect(videos[currentIndex + 1]);
      }
    } catch (error) {
      console.error('Error handling video end:', error);
    }
  }, [videos, currentVideo, handleVideoSelect]);

  const handleVideoError = useCallback((videoId) => {
    toast({
      title: "خطأ في تشغيل الفيديو",
      description: "حدث خطأ أثناء محاولة تشغيل الفيديو",
      variant: "destructive"
    });
    handleVideoRemove(videoId);
  }, [handleVideoRemove]);

  return (
    <div className="min-h-screen gradient-bg">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl floating-animation" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl floating-animation" style={{ animationDelay: '3s' }} />
      </div>

      <div className="relative z-10">
        <header className="p-6">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center pulse-glow">
                  <Play className="w-6 h-6 text-white" />
                </div>
                <Sparkles className="absolute -top-1 -right-1 w-4 h-4 text-yellow-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">مشغل الفيديو المتقدم</h1>
                <p className="text-gray-300">يدعم جميع صيغ الفيديو</p>
              </div>
            </div>

            <div className="flex gap-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowPlaylist(!showPlaylist)}
                className="glass-effect px-4 py-2 rounded-lg text-white flex items-center gap-2"
              >
                <List className="w-5 h-5" />
                قائمة التشغيل
              </motion.button>
            </div>
          </motion.div>
        </header>

        <div className="px-6 pb-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-3">
              <AnimatePresence mode="wait">
                {currentVideo ? (
                  <motion.div
                    key={currentVideo.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="aspect-video"
                  >
                    <VideoPlayer
                      key={currentVideo.id}
                      src={currentVideo.url}
                      title={currentVideo.name}
                      onEnded={handleVideoEnded}
                      onError={() => handleVideoError(currentVideo.id)}
                      className="w-full h-full"
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="aspect-video"
                  >
                    <FileUpload
                      onFileSelect={handleFileSelect}
                      className="w-full h-full flex items-center justify-center"
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              {currentVideo && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-6"
                >
                  <FileUpload
                    onFileSelect={handleFileSelect}
                    className="h-32"
                  />
                </motion.div>
              )}
            </div>

            <AnimatePresence>
              {showPlaylist && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="lg:col-span-1"
                >
                  <div className="glass-effect rounded-xl p-6 h-fit max-h-[80vh] overflow-y-auto">
                    <Playlist
                      videos={videos}
                      currentVideo={currentVideo}
                      onVideoSelect={handleVideoSelect}
                      onVideoRemove={handleVideoRemove}
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {videos.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="px-6 pb-6"
          >
            <div className="glass-effect rounded-xl p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-3xl font-bold text-white">{videos.length}</div>
                  <div className="text-gray-300">مقاطع الفيديو</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-white">
                    {(videos.reduce((acc, v) => acc + v.size, 0) / (1024 * 1024 * 1024)).toFixed(1)}
                  </div>
                  <div className="text-gray-300">جيجابايت</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-white">
                    {new Set(videos.map(v => v.type)).size}
                  </div>
                  <div className="text-gray-300">صيغ مختلفة</div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      <Toaster />
    </div>
  );
}

export default App;