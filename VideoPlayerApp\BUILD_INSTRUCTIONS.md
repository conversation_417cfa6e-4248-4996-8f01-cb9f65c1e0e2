# تعليمات بناء تطبيق الأندرويد

## الطريقة الأولى: استخدام Expo Application Services (EAS)

### 1. تثبيت EAS CLI
```bash
npm install -g @expo/eas-cli
```

### 2. تسجيل الدخول إلى Expo
```bash
eas login
```

### 3. إعداد المشروع
```bash
cd VideoPlayerApp
eas build:configure
```

### 4. بناء APK للأندرويد
```bash
# بناء APK للتطوير
eas build --platform android --profile development

# بناء APK للإنتاج
eas build --platform android --profile production
```

### 5. تحميل APK
بعد اكتمال البناء، ستحصل على رابط لتحميل ملف APK.

## الطريقة الثانية: البناء المحلي

### المتطلبات
- Android Studio
- Java Development Kit (JDK) 11 أو أحدث
- Android SDK

### 1. إعد<PERSON> البيئة
```bash
# تثبيت Expo CLI
npm install -g @expo/cli

# إنشاء مشروع محلي
cd VideoPlayerApp
npx expo run:android
```

### 2. إنشاء مجلد Android
```bash
npx expo prebuild --platform android
```

### 3. بناء APK
```bash
cd android
./gradlew assembleRelease
```

ستجد ملف APK في:
`android/app/build/outputs/apk/release/app-release.apk`

## الطريقة الثالثة: استخدام Expo Classic Build

### 1. تثبيت Expo CLI القديم
```bash
npm install -g expo-cli
```

### 2. بناء APK
```bash
cd VideoPlayerApp
expo build:android
```

### 3. اختيار نوع البناء
- APK: للتوزيع المباشر
- AAB: لرفع على Google Play Store

## إعدادات إضافية

### تخصيص الأيقونة
1. استبدل الملفات في مجلد `assets/`
2. تأكد من الأحجام الصحيحة:
   - icon.png: 1024x1024
   - adaptive-icon.png: 1024x1024
   - splash-icon.png: 1284x2778

### إعدادات التوقيع (للإنتاج)
```json
{
  "expo": {
    "android": {
      "package": "com.videoplayer.advanced",
      "versionCode": 1
    }
  }
}
```

## اختبار التطبيق

### على الجهاز الحقيقي
1. فعل "Developer Options" في الأندرويد
2. فعل "USB Debugging"
3. وصل الجهاز بالكمبيوتر
4. شغل `npx expo run:android`

### على المحاكي
1. شغل Android Studio
2. افتح AVD Manager
3. أنشئ محاكي جديد
4. شغل `npx expo run:android`

## حل المشاكل الشائعة

### خطأ في الذاكرة
```bash
export JAVA_OPTS="-Xmx4096m"
```

### خطأ في Gradle
```bash
cd android
./gradlew clean
./gradlew assembleRelease
```

### خطأ في Metro
```bash
npx expo start --clear
```

## نصائح للأداء

1. **تحسين الصور**: استخدم صيغ مضغوطة
2. **تقليل حجم Bundle**: احذف المكتبات غير المستخدمة
3. **ProGuard**: فعل تصغير الكود للإنتاج

## رفع على Google Play Store

### 1. إنشاء AAB
```bash
eas build --platform android --profile production
```

### 2. إعداد Google Play Console
1. أنشئ حساب مطور
2. أنشئ تطبيق جديد
3. ارفع ملف AAB
4. املأ معلومات التطبيق

### 3. الصلاحيات المطلوبة
تأكد من إضافة الصلاحيات في `app.json`:
```json
{
  "android": {
    "permissions": [
      "READ_EXTERNAL_STORAGE",
      "WRITE_EXTERNAL_STORAGE"
    ]
  }
}
```

## الدعم

إذا واجهت مشاكل:
1. تحقق من [وثائق Expo](https://docs.expo.dev/)
2. ابحث في [منتدى Expo](https://forums.expo.dev/)
3. راجع [مشاكل GitHub](https://github.com/expo/expo/issues)
