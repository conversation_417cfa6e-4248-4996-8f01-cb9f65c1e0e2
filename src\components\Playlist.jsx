
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Trash2, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Playlist = ({ videos, currentVideo, onVideoSelect, onVideoRemove, className }) => {
  const formatDuration = (duration) => {
    if (!duration) return '00:00';
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <h2 className="text-2xl font-bold text-white mb-6">قائمة التشغيل</h2>
      
      <AnimatePresence>
        {videos.map((video, index) => (
          <motion.div
            key={video.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ delay: index * 0.1 }}
            className={`playlist-item rounded-lg p-4 cursor-pointer ${
              currentVideo?.id === video.id ? 'active' : ''
            }`}
            onClick={() => onVideoSelect(video)}
          >
            <div className="flex items-center gap-4">
              <div className="relative flex-shrink-0">
                <div className="w-16 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                  <Play className="w-6 h-6 text-white" />
                </div>
                {currentVideo?.id === video.id && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <div className="w-2 h-2 bg-white rounded-full" />
                  </motion.div>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <h3 className="text-white font-medium truncate">{video.name}</h3>
                <div className="flex items-center gap-2 text-sm text-gray-400 mt-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatDuration(video.duration)}</span>
                  <span>•</span>
                  <span>{(video.size / (1024 * 1024)).toFixed(1)} MB</span>
                </div>
              </div>

              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onVideoRemove(video.id);
                }}
                className="text-gray-400 hover:text-red-400 hover:bg-red-400/10"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {videos.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Play className="w-8 h-8 text-gray-400" />
          </div>
          <p className="text-gray-400">لا توجد مقاطع فيديو في القائمة</p>
        </motion.div>
      )}
    </div>
  );
};

export default Playlist;
