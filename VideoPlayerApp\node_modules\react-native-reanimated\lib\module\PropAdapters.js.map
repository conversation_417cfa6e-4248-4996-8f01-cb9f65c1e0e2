{"version": 3, "names": ["addWhitelistedNativeProps", "createAnimatedPropAdapter", "adapter", "nativeProps", "nativePropsToAdd", "for<PERSON>ach", "prop"], "sourceRoot": "../../src", "sources": ["PropAdapters.ts"], "mappings": "AAAA,YAAY;;AAKZ,SAASA,yBAAyB,QAAQ,mBAAgB;;AAE1D;;AAMA,OAAO,SAASC,yBAAyBA,CACvCC,OAAoC,EACpCC,WAAsB,EACO;EAC7B,MAAMC,gBAA4C,GAAG,CAAC,CAAC;EACvDD,WAAW,EAAEE,OAAO,CAAEC,IAAI,IAAK;IAC7BF,gBAAgB,CAACE,IAAI,CAAC,GAAG,IAAI;EAC/B,CAAC,CAAC;EACFN,yBAAyB,CAACI,gBAAgB,CAAC;EAC3C,OAAOF,OAAO;AAChB", "ignoreList": []}