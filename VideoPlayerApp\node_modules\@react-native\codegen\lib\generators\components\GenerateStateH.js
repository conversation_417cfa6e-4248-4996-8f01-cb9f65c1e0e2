/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 */

'use strict';

// File path -> contents

const FileTemplate = ({libraryName, stateClasses}) =>
  `
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * ${'@'}generated by codegen project: GenerateStateH.js
 */
#pragma once

#ifdef ANDROID
#include <folly/dynamic.h>
#endif

namespace facebook::react {

${stateClasses}

} // namespace facebook::react
`.trim();
const StateTemplate = ({stateName}) =>
  `
class ${stateName}State {
public:
  ${stateName}State() = default;

#ifdef ANDROID
  ${stateName}State(${stateName}State const &previousState, folly::dynamic data){};
  folly::dynamic getDynamic() const {
    return {};
  };
#endif
};
`.trim();
module.exports = {
  generate(
    libraryName,
    schema,
    packageName,
    assumeNonnull = false,
    headerPrefix,
  ) {
    const fileName = 'States.h';
    const stateClasses = Object.keys(schema.modules)
      .map(moduleName => {
        const module = schema.modules[moduleName];
        if (module.type !== 'Component') {
          return;
        }
        const components = module.components;
        // No components in this module
        if (components == null) {
          return null;
        }
        return Object.keys(components)
          .map(componentName => {
            const component = components[componentName];
            if (component.interfaceOnly === true) {
              return null;
            }
            return StateTemplate({
              stateName: componentName,
            });
          })
          .filter(Boolean)
          .join('\n\n');
      })
      .filter(Boolean)
      .join('\n\n');
    const template = FileTemplate({
      libraryName,
      stateClasses,
    });
    return new Map([[fileName, template]]);
  },
};
