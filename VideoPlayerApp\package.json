{"name": "videoplayerapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "expo": "~53.0.9", "expo-av": "^15.1.4", "expo-document-picker": "^13.1.5", "expo-linear-gradient": "^14.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-reanimated": "~3.17.4", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}