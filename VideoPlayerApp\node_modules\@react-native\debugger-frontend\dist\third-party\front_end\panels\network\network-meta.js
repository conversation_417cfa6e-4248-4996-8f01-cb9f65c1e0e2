import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as o from"../../core/root/root.js";import*as r from"../../core/sdk/sdk.js";import*as n from"../../models/extensions/extensions.js";import*as a from"../../models/workspace/workspace.js";import*as i from"../../ui/legacy/legacy.js";import*as s from"../timeline/utils/utils.js";import*as l from"./forward/forward.js";const c={showNetwork:"Show Network",network:"Network",networkExpoUnstable:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},w=t.i18n.registerUIStrings("panels/network/network-meta.ts",c),d=t.i18n.getLazilyComputedLocalizedString.bind(void 0,w),k=t.i18n.getLocalizedString.bind(void 0,w);let g;async function u(){return g||(g=await import("./network.js")),g}function R(e){return void 0===g?[]:e(g)}i.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:d(c.showNetwork),title:()=>o.Runtime.experiments.isEnabled(o.Runtime.RNExperimentName.ENABLE_NETWORK_PANEL)?k(c.network):k(c.networkExpoUnstable),order:40,isPreviewFeature:!0,condition:o.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await u()).NetworkPanel.NetworkPanel.instance()}),i.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:d(c.showNetworkRequestBlocking),title:d(c.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await u()).BlockedURLsPane.BlockedURLsPane)}),i.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:d(c.showNetworkConditions),title:d(c.networkConditions),persistence:"closeable",order:40,tags:[d(c.diskCache),d(c.networkThrottling),t.i18n.lockedLazyString("useragent"),t.i18n.lockedLazyString("user agent"),t.i18n.lockedLazyString("user-agent")],loadView:async()=>(await u()).NetworkConfigView.NetworkConfigView.instance()}),i.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:d(c.showSearch),title:d(c.search),persistence:"permanent",loadView:async()=>(await u()).NetworkPanel.SearchNetworkView.instance()}),i.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>R((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await u()).NetworkPanel.ActionDelegate),options:[{value:!0,title:d(c.recordNetworkLog)},{value:!1,title:d(c.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),i.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:d(c.clear),iconClass:"clear",loadActionDelegate:async()=>new((await u()).NetworkPanel.ActionDelegate),contextTypes:()=>R((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),i.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:d(c.hideRequestDetails),contextTypes:()=>R((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await u()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),i.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:d(c.search),contextTypes:()=>R((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await u()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),i.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:d(c.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>R((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await u()).BlockedURLsPane.ActionDelegate)}),i.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:d(c.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>R((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await u()).BlockedURLsPane.ActionDelegate)}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:d(c.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[d(c.colorCode),d(c.resourceType)],options:[{value:!0,title:d(c.colorCodeByResourceType)},{value:!1,title:d(c.useDefaultColors)}]}),e.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:d(c.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[d(c.netWork),d(c.frame),d(c.group)],options:[{value:!0,title:d(c.groupNetworkLogItemsByFrame)},{value:!1,title:d(c.dontGroupNetworkLogItemsByFrame)}]}),i.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await u()).NetworkPanel.NetworkPanel.instance()}),i.ContextMenu.registerProvider({contextTypes:()=>[r.NetworkRequest.NetworkRequest,r.Resource.Resource,a.UISourceCode.UISourceCode,s.NetworkRequest.TimelineNetworkRequest],loadProvider:async()=>(await u()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),e.Revealer.registerRevealer({contextTypes:()=>[r.NetworkRequest.NetworkRequest],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await u()).NetworkPanel.RequestRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[l.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await u()).NetworkPanel.RequestLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[l.NetworkRequestId.NetworkRequestId],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await u()).NetworkPanel.RequestIdRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[l.UIFilter.UIRequestFilter,n.ExtensionServer.RevealableNetworkRequestFilter],destination:e.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await u()).NetworkPanel.NetworkLogWithFilterRevealer)});
