<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            justify-content: center;
        }
        .icon-container {
            text-align: center;
        }
        .icon {
            border: 2px solid #ddd;
            border-radius: 10px;
            margin-bottom: 10px;
        }
        button {
            background: linear-gradient(45deg, #8b5cf6, #3b82f6);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 إنشاء أيقونات التطبيق</h1>
        
        <div class="instructions">
            <h3>📋 التعليمات:</h3>
            <ol>
                <li>اضغط على "إنشاء الأيقونات" لتوليد الأيقونات</li>
                <li>انقر بالزر الأيمن على كل أيقونة واختر "حفظ الصورة باسم"</li>
                <li>احفظ الأيقونة الأولى باسم "icon-192.png"</li>
                <li>احفظ الأيقونة الثانية باسم "icon-512.png"</li>
                <li>ضع الملفين في نفس مجلد التطبيق</li>
            </ol>
        </div>

        <div style="text-align: center;">
            <button onclick="generateIcons()">🎨 إنشاء الأيقونات</button>
        </div>

        <div id="iconPreview" class="icon-preview" style="display: none;">
            <div class="icon-container">
                <canvas id="icon192" class="icon" width="192" height="192"></canvas>
                <div><strong>192x192 بكسل</strong></div>
                <div>للاستخدام العام</div>
            </div>
            <div class="icon-container">
                <canvas id="icon512" class="icon" width="512" height="512"></canvas>
                <div><strong>512x512 بكسل</strong></div>
                <div>للجودة العالية</div>
            </div>
        </div>

        <div class="instructions">
            <h3>🔧 تخصيص الأيقونات:</h3>
            <p>يمكنك تعديل الألوان والتصميم في الكود أدناه، أو استخدام أدوات تصميم مثل:</p>
            <ul>
                <li><strong>Canva</strong>: https://canva.com/</li>
                <li><strong>GIMP</strong>: برنامج مجاني لتحرير الصور</li>
                <li><strong>Icon Kitchen</strong>: https://icon.kitchen/</li>
            </ul>
        </div>
    </div>

    <script>
        function generateIcons() {
            // إنشاء أيقونة 192x192
            const canvas192 = document.getElementById('icon192');
            const ctx192 = canvas192.getContext('2d');
            drawIcon(ctx192, 192);

            // إنشاء أيقونة 512x512
            const canvas512 = document.getElementById('icon512');
            const ctx512 = canvas512.getContext('2d');
            drawIcon(ctx512, 512);

            // إظهار الأيقونات
            document.getElementById('iconPreview').style.display = 'flex';
        }

        function drawIcon(ctx, size) {
            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#1e1b4b');
            gradient.addColorStop(0.5, '#3b82f6');
            gradient.addColorStop(1, '#8b5cf6');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // إضافة حدود دائرية
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.1);
            ctx.clip();

            // رسم رمز التشغيل
            const centerX = size / 2;
            const centerY = size / 2;
            const triangleSize = size * 0.3;

            // دائرة خلفية للرمز
            ctx.beginPath();
            ctx.arc(centerX, centerY, triangleSize * 0.8, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.fill();

            // رمز التشغيل (مثلث)
            ctx.beginPath();
            ctx.moveTo(centerX - triangleSize * 0.3, centerY - triangleSize * 0.4);
            ctx.lineTo(centerX - triangleSize * 0.3, centerY + triangleSize * 0.4);
            ctx.lineTo(centerX + triangleSize * 0.4, centerY);
            ctx.closePath();
            ctx.fillStyle = 'white';
            ctx.fill();

            // إضافة تأثير لمعان
            const glowGradient = ctx.createRadialGradient(
                centerX, centerY * 0.7, 0,
                centerX, centerY * 0.7, size * 0.6
            );
            glowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
            glowGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            
            ctx.fillStyle = glowGradient;
            ctx.fillRect(0, 0, size, size);

            // إضافة نص صغير (اختياري)
            if (size >= 512) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.font = `${size * 0.08}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillText('مشغل الفيديو', centerX, size * 0.85);
            }
        }

        // إضافة دعم للحدود الدائرية في المتصفحات القديمة
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
