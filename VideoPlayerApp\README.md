# مشغل الفيديو المتقدم - Advanced Video Player

تطبيق أندرويد متقدم لتشغيل الفيديوهات مبني بـ React Native و Expo.

## المميزات

### 🎥 تشغيل الفيديو
- دعم جميع صيغ الفيديو الشائعة (MP4, WebM, OGG, MOV, MKV, AVI, WMV, 3GP, FLV)
- مشغل فيديو متقدم مع عناصر تحكم كاملة
- تشغيل تلقائي للفيديو التالي
- إمكانية التحكم في السرعة والصوت

### 📁 إدارة الملفات
- رفع الملفات من الجهاز باستخدام Document Picker
- دعم رفع ملفات متعددة
- التحقق من صيغة وحجم الملفات
- حد أقصى 500 ميجابايت لكل ملف

### 📋 قائمة التشغيل
- قائمة تشغيل ديناميكية
- عرض معلومات الملف (الاسم، المدة، الحجم)
- إمكانية حذف الفيديوهات من القائمة
- تمييز الفيديو الحالي

### 💾 التخزين المحلي
- حفظ قائمة التشغيل تلقائياً
- استعادة آخر فيديو تم تشغيله
- إدارة متقدمة للبيانات المحفوظة

### 🎨 واجهة المستخدم
- تصميم عصري وجذاب
- دعم الوضع المظلم
- أنيميشن سلسة باستخدام React Native Reanimated
- واجهة باللغة العربية

## التقنيات المستخدمة

- **React Native** - إطار العمل الأساسي
- **Expo** - منصة التطوير والنشر
- **Expo AV** - تشغيل الفيديو والصوت
- **Expo Document Picker** - اختيار الملفات
- **AsyncStorage** - التخزين المحلي
- **React Native Reanimated** - الأنيميشن
- **React Native Vector Icons** - الأيقونات

## متطلبات النظام

- Node.js 16 أو أحدث
- npm أو yarn
- Expo CLI
- Android Studio (للتطوير على الأندرويد)
- جهاز أندرويد أو محاكي

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
npm install -g expo-cli
```

### 2. تثبيت المكتبات
```bash
cd VideoPlayerApp
npm install
```

### 3. تشغيل التطبيق
```bash
# تشغيل على الأندرويد
npm run android

# تشغيل على iOS (يتطلب macOS)
npm run ios

# تشغيل على الويب
npm run web
```

## بناء التطبيق للإنتاج

### بناء APK للأندرويد
```bash
expo build:android
```

### بناء AAB للأندرويد (Google Play Store)
```bash
expo build:android -t app-bundle
```

## هيكل المشروع

```
VideoPlayerApp/
├── src/
│   ├── components/
│   │   ├── VideoPlayer.js      # مكون مشغل الفيديو
│   │   ├── FileUpload.js       # مكون رفع الملفات
│   │   └── Playlist.js         # مكون قائمة التشغيل
│   └── utils/
│       └── storage.js          # إدارة التخزين المحلي
├── assets/                     # الصور والأيقونات
├── App.js                      # التطبيق الرئيسي
├── app.json                    # إعدادات Expo
└── package.json               # المكتبات والاعتمادات
```

## الصلاحيات المطلوبة

- `READ_EXTERNAL_STORAGE` - قراءة الملفات من التخزين
- `WRITE_EXTERNAL_STORAGE` - كتابة الملفات في التخزين
- `CAMERA` - الوصول للكاميرا (اختياري)
- `RECORD_AUDIO` - تسجيل الصوت (اختياري)

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التغييرات والاختبار
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح Issue في GitHub.

---

تم تطوير هذا التطبيق بـ ❤️ باستخدام React Native و Expo
