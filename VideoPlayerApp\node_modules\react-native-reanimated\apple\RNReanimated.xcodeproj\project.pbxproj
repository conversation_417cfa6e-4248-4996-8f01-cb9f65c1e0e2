// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		440FEC2C209062F100EEC73A /* REAPropsNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC0D209062F100EEC73A /* REAPropsNode.m */; };
		440FEC2D209062F100EEC73A /* REAStyleNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC13209062F100EEC73A /* REAStyleNode.m */; };
		440FEC2E209062F100EEC73A /* READebugNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC14209062F100EEC73A /* READebugNode.m */; };
		440FEC2F209062F100EEC73A /* REAClockNodes.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC18209062F100EEC73A /* REAClockNodes.m */; };
		440FEC30209062F100EEC73A /* REAValueNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC19209062F100EEC73A /* REAValueNode.m */; };
		440FEC31209062F100EEC73A /* REACondNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1A209062F100EEC73A /* REACondNode.m */; };
		440FEC32209062F100EEC73A /* REAJSCallNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1B209062F100EEC73A /* REAJSCallNode.m */; };
		440FEC33209062F100EEC73A /* REABezierNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1C209062F100EEC73A /* REABezierNode.m */; };
		440FEC34209062F100EEC73A /* REANode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1F209062F100EEC73A /* REANode.m */; };
		440FEC35209062F100EEC73A /* REATransformNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC20209062F100EEC73A /* REATransformNode.m */; };
		440FEC36209062F100EEC73A /* REAEventNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC21209062F100EEC73A /* REAEventNode.m */; };
		440FEC37209062F100EEC73A /* REASetNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC23209062F100EEC73A /* REASetNode.m */; };
		440FEC38209062F100EEC73A /* REAOperatorNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC24209062F100EEC73A /* REAOperatorNode.m */; };
		440FEC39209062F100EEC73A /* REABlockNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC25209062F100EEC73A /* REABlockNode.m */; };
		440FEC3A209062F100EEC73A /* REAModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC2A209062F100EEC73A /* REAModule.m */; };
		440FEC3B209062F100EEC73A /* REANodesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC2B209062F100EEC73A /* REANodesManager.m */; };
		44125DC022538E6D003C1762 /* REATransitionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DBF22538E6D003C1762 /* REATransitionManager.m */; };
		44125DC322538F68003C1762 /* REATransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DC222538F68003C1762 /* REATransition.m */; };
		44125DC82253906B003C1762 /* REAAllTransitions.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DC72253906A003C1762 /* REAAllTransitions.m */; };
		44125DCB22539177003C1762 /* REATransitionAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DCA22539177003C1762 /* REATransitionAnimation.m */; };
		44125DCE2253A038003C1762 /* REATransitionValues.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DCD2253A038003C1762 /* REATransitionValues.m */; };
		44125DD12253A3C0003C1762 /* RCTConvert+REATransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DD02253A3C0003C1762 /* RCTConvert+REATransition.m */; };
		660A44292119B821006BFD5E /* REAConcatNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 660A44282119B820006BFD5E /* REAConcatNode.m */; };
		66240C6920C68DEA00648F55 /* REAAlwaysNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 66240C6820C68DEA00648F55 /* REAAlwaysNode.m */; };
		A12DA6B722EC228D00E8271A /* REAParamNode.m in Sources */ = {isa = PBXBuildFile; fileRef = A12DA6B622EC228D00E8271A /* REAParamNode.m */; };
		A12DA6B822EC228D00E8271A /* REAParamNode.m in Sources */ = {isa = PBXBuildFile; fileRef = A12DA6B622EC228D00E8271A /* REAParamNode.m */; };
		A12DA6BE22EC22E400E8271A /* REAFunctionNode.m in Sources */ = {isa = PBXBuildFile; fileRef = A12DA6BA22EC22E300E8271A /* REAFunctionNode.m */; };
		A12DA6BF22EC22E400E8271A /* REAFunctionNode.m in Sources */ = {isa = PBXBuildFile; fileRef = A12DA6BA22EC22E300E8271A /* REAFunctionNode.m */; };
		A12DA6C022EC22E400E8271A /* REACallFuncNode.m in Sources */ = {isa = PBXBuildFile; fileRef = A12DA6BC22EC22E300E8271A /* REACallFuncNode.m */; };
		A12DA6C122EC22E400E8271A /* REACallFuncNode.m in Sources */ = {isa = PBXBuildFile; fileRef = A12DA6BC22EC22E300E8271A /* REACallFuncNode.m */; };
		FDBB1777229BF0DE00D1E455 /* REAModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC2A209062F100EEC73A /* REAModule.m */; };
		FDBB1778229BF0DE00D1E455 /* REANodesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC2B209062F100EEC73A /* REANodesManager.m */; };
		FDBB1779229BF0E700D1E455 /* REATransitionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DBF22538E6D003C1762 /* REATransitionManager.m */; };
		FDBB177A229BF0E700D1E455 /* REATransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DC222538F68003C1762 /* REATransition.m */; };
		FDBB177B229BF0E700D1E455 /* REAAllTransitions.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DC72253906A003C1762 /* REAAllTransitions.m */; };
		FDBB177C229BF0E700D1E455 /* REATransitionAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DCA22539177003C1762 /* REATransitionAnimation.m */; };
		FDBB177D229BF0E700D1E455 /* REATransitionValues.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DCD2253A038003C1762 /* REATransitionValues.m */; };
		FDBB177E229BF0E700D1E455 /* RCTConvert+REATransition.m in Sources */ = {isa = PBXBuildFile; fileRef = 44125DD02253A3C0003C1762 /* RCTConvert+REATransition.m */; };
		FDE6D933229BF70F007F6716 /* REAConcatNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 660A44282119B820006BFD5E /* REAConcatNode.m */; };
		FDE6D934229BF70F007F6716 /* REAAlwaysNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 66240C6820C68DEA00648F55 /* REAAlwaysNode.m */; };
		FDE6D935229BF70F007F6716 /* REANode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1F209062F100EEC73A /* REANode.m */; };
		FDE6D936229BF70F007F6716 /* REAValueNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC19209062F100EEC73A /* REAValueNode.m */; };
		FDE6D937229BF70F007F6716 /* REAStyleNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC13209062F100EEC73A /* REAStyleNode.m */; };
		FDE6D938229BF70F007F6716 /* REATransformNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC20209062F100EEC73A /* REATransformNode.m */; };
		FDE6D939229BF70F007F6716 /* REAPropsNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC0D209062F100EEC73A /* REAPropsNode.m */; };
		FDE6D93A229BF70F007F6716 /* REABlockNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC25209062F100EEC73A /* REABlockNode.m */; };
		FDE6D93B229BF70F007F6716 /* REACondNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1A209062F100EEC73A /* REACondNode.m */; };
		FDE6D93C229BF70F007F6716 /* REAOperatorNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC24209062F100EEC73A /* REAOperatorNode.m */; };
		FDE6D93D229BF70F007F6716 /* REASetNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC23209062F100EEC73A /* REASetNode.m */; };
		FDE6D93E229BF70F007F6716 /* READebugNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC14209062F100EEC73A /* READebugNode.m */; };
		FDE6D93F229BF70F007F6716 /* REAClockNodes.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC18209062F100EEC73A /* REAClockNodes.m */; };
		FDE6D940229BF70F007F6716 /* REAJSCallNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1B209062F100EEC73A /* REAJSCallNode.m */; };
		FDE6D941229BF70F007F6716 /* REABezierNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC1C209062F100EEC73A /* REABezierNode.m */; };
		FDE6D942229BF70F007F6716 /* REAEventNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 440FEC21209062F100EEC73A /* REAEventNode.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		58B511D91A9E6C8500147676 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDBB176C229BF04900D1E455 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		134814201AA4EA6300B7C361 /* libRNReanimated.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNReanimated.a; sourceTree = BUILT_PRODUCTS_DIR; };
		440FEC01209062F100EEC73A /* REANodesManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REANodesManager.h; sourceTree = "<group>"; };
		440FEC0A209062F100EEC73A /* REAModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAModule.h; sourceTree = "<group>"; };
		440FEC0D209062F100EEC73A /* REAPropsNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAPropsNode.m; sourceTree = "<group>"; };
		440FEC0E209062F100EEC73A /* REABezierNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REABezierNode.h; sourceTree = "<group>"; };
		440FEC0F209062F100EEC73A /* REAJSCallNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAJSCallNode.h; sourceTree = "<group>"; };
		440FEC10209062F100EEC73A /* REAEventNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAEventNode.h; sourceTree = "<group>"; };
		440FEC11209062F100EEC73A /* REANode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REANode.h; sourceTree = "<group>"; };
		440FEC12209062F100EEC73A /* REATransformNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REATransformNode.h; sourceTree = "<group>"; };
		440FEC13209062F100EEC73A /* REAStyleNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAStyleNode.m; sourceTree = "<group>"; };
		440FEC14209062F100EEC73A /* READebugNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = READebugNode.m; sourceTree = "<group>"; };
		440FEC15209062F100EEC73A /* REABlockNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REABlockNode.h; sourceTree = "<group>"; };
		440FEC16209062F100EEC73A /* REAOperatorNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAOperatorNode.h; sourceTree = "<group>"; };
		440FEC17209062F100EEC73A /* REASetNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REASetNode.h; sourceTree = "<group>"; };
		440FEC18209062F100EEC73A /* REAClockNodes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAClockNodes.m; sourceTree = "<group>"; };
		440FEC19209062F100EEC73A /* REAValueNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAValueNode.m; sourceTree = "<group>"; };
		440FEC1A209062F100EEC73A /* REACondNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REACondNode.m; sourceTree = "<group>"; };
		440FEC1B209062F100EEC73A /* REAJSCallNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAJSCallNode.m; sourceTree = "<group>"; };
		440FEC1C209062F100EEC73A /* REABezierNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REABezierNode.m; sourceTree = "<group>"; };
		440FEC1D209062F100EEC73A /* REAPropsNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAPropsNode.h; sourceTree = "<group>"; };
		440FEC1E209062F100EEC73A /* REAStyleNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAStyleNode.h; sourceTree = "<group>"; };
		440FEC1F209062F100EEC73A /* REANode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REANode.m; sourceTree = "<group>"; };
		440FEC20209062F100EEC73A /* REATransformNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REATransformNode.m; sourceTree = "<group>"; };
		440FEC21209062F100EEC73A /* REAEventNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAEventNode.m; sourceTree = "<group>"; };
		440FEC22209062F100EEC73A /* REAClockNodes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAClockNodes.h; sourceTree = "<group>"; };
		440FEC23209062F100EEC73A /* REASetNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REASetNode.m; sourceTree = "<group>"; };
		440FEC24209062F100EEC73A /* REAOperatorNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAOperatorNode.m; sourceTree = "<group>"; };
		440FEC25209062F100EEC73A /* REABlockNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REABlockNode.m; sourceTree = "<group>"; };
		440FEC26209062F100EEC73A /* READebugNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = READebugNode.h; sourceTree = "<group>"; };
		440FEC27209062F100EEC73A /* REACondNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REACondNode.h; sourceTree = "<group>"; };
		440FEC28209062F100EEC73A /* REAValueNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAValueNode.h; sourceTree = "<group>"; };
		440FEC2A209062F100EEC73A /* REAModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAModule.m; sourceTree = "<group>"; };
		440FEC2B209062F100EEC73A /* REANodesManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REANodesManager.m; sourceTree = "<group>"; };
		44125DBE22538E6D003C1762 /* REATransitionManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = REATransitionManager.h; sourceTree = "<group>"; };
		44125DBF22538E6D003C1762 /* REATransitionManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = REATransitionManager.m; sourceTree = "<group>"; };
		44125DC122538F68003C1762 /* REATransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = REATransition.h; sourceTree = "<group>"; };
		44125DC222538F68003C1762 /* REATransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = REATransition.m; sourceTree = "<group>"; };
		44125DC62253906A003C1762 /* REAAllTransitions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = REAAllTransitions.h; sourceTree = "<group>"; };
		44125DC72253906A003C1762 /* REAAllTransitions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = REAAllTransitions.m; sourceTree = "<group>"; };
		44125DC922539177003C1762 /* REATransitionAnimation.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = REATransitionAnimation.h; sourceTree = "<group>"; };
		44125DCA22539177003C1762 /* REATransitionAnimation.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = REATransitionAnimation.m; sourceTree = "<group>"; };
		44125DCC2253A038003C1762 /* REATransitionValues.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = REATransitionValues.h; sourceTree = "<group>"; };
		44125DCD2253A038003C1762 /* REATransitionValues.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = REATransitionValues.m; sourceTree = "<group>"; };
		44125DCF2253A3C0003C1762 /* RCTConvert+REATransition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RCTConvert+REATransition.h"; sourceTree = "<group>"; };
		44125DD02253A3C0003C1762 /* RCTConvert+REATransition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "RCTConvert+REATransition.m"; sourceTree = "<group>"; };
		4DA383A8226FA6A400582919 /* REAUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = REAUtils.h; sourceTree = "<group>"; };
		660A44282119B820006BFD5E /* REAConcatNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAConcatNode.m; sourceTree = "<group>"; };
		660A442A2119B83E006BFD5E /* REAConcatNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAConcatNode.h; sourceTree = "<group>"; };
		66240C6720C68DEA00648F55 /* REAAlwaysNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAAlwaysNode.h; sourceTree = "<group>"; };
		66240C6820C68DEA00648F55 /* REAAlwaysNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAAlwaysNode.m; sourceTree = "<group>"; };
		A12DA6B622EC228D00E8271A /* REAParamNode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = REAParamNode.m; sourceTree = "<group>"; };
		A12DA6B922EC22A900E8271A /* REAParamNode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = REAParamNode.h; sourceTree = "<group>"; };
		A12DA6BA22EC22E300E8271A /* REAFunctionNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REAFunctionNode.m; sourceTree = "<group>"; };
		A12DA6BB22EC22E300E8271A /* REACallFuncNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REACallFuncNode.h; sourceTree = "<group>"; };
		A12DA6BC22EC22E300E8271A /* REACallFuncNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = REACallFuncNode.m; sourceTree = "<group>"; };
		A12DA6BD22EC22E300E8271A /* REAFunctionNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = REAFunctionNode.h; sourceTree = "<group>"; };
		FDBB176E229BF04900D1E455 /* libRNReanimated-tvOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libRNReanimated-tvOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		58B511D81A9E6C8500147676 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDBB176B229BF04900D1E455 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		134814211AA4EA7D00B7C361 /* Products */ = {
			isa = PBXGroup;
			children = (
				134814201AA4EA6300B7C361 /* libRNReanimated.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		440FEC0C209062F100EEC73A /* Nodes */ = {
			isa = PBXGroup;
			children = (
				A12DA6B922EC22A900E8271A /* REAParamNode.h */,
				A12DA6B622EC228D00E8271A /* REAParamNode.m */,
				A12DA6BB22EC22E300E8271A /* REACallFuncNode.h */,
				A12DA6BC22EC22E300E8271A /* REACallFuncNode.m */,
				A12DA6BD22EC22E300E8271A /* REAFunctionNode.h */,
				A12DA6BA22EC22E300E8271A /* REAFunctionNode.m */,
				660A442A2119B83E006BFD5E /* REAConcatNode.h */,
				660A44282119B820006BFD5E /* REAConcatNode.m */,
				66240C6720C68DEA00648F55 /* REAAlwaysNode.h */,
				66240C6820C68DEA00648F55 /* REAAlwaysNode.m */,
				440FEC11209062F100EEC73A /* REANode.h */,
				440FEC1F209062F100EEC73A /* REANode.m */,
				440FEC28209062F100EEC73A /* REAValueNode.h */,
				440FEC19209062F100EEC73A /* REAValueNode.m */,
				440FEC1E209062F100EEC73A /* REAStyleNode.h */,
				440FEC13209062F100EEC73A /* REAStyleNode.m */,
				440FEC12209062F100EEC73A /* REATransformNode.h */,
				440FEC20209062F100EEC73A /* REATransformNode.m */,
				440FEC1D209062F100EEC73A /* REAPropsNode.h */,
				440FEC0D209062F100EEC73A /* REAPropsNode.m */,
				440FEC15209062F100EEC73A /* REABlockNode.h */,
				440FEC25209062F100EEC73A /* REABlockNode.m */,
				440FEC27209062F100EEC73A /* REACondNode.h */,
				440FEC1A209062F100EEC73A /* REACondNode.m */,
				440FEC16209062F100EEC73A /* REAOperatorNode.h */,
				440FEC24209062F100EEC73A /* REAOperatorNode.m */,
				440FEC17209062F100EEC73A /* REASetNode.h */,
				440FEC23209062F100EEC73A /* REASetNode.m */,
				440FEC26209062F100EEC73A /* READebugNode.h */,
				440FEC14209062F100EEC73A /* READebugNode.m */,
				440FEC22209062F100EEC73A /* REAClockNodes.h */,
				440FEC18209062F100EEC73A /* REAClockNodes.m */,
				440FEC0F209062F100EEC73A /* REAJSCallNode.h */,
				440FEC1B209062F100EEC73A /* REAJSCallNode.m */,
				440FEC0E209062F100EEC73A /* REABezierNode.h */,
				440FEC1C209062F100EEC73A /* REABezierNode.m */,
				440FEC10209062F100EEC73A /* REAEventNode.h */,
				440FEC21209062F100EEC73A /* REAEventNode.m */,
			);
			path = Nodes;
			sourceTree = "<group>";
		};
		44125C76224FBE0B003C1762 /* Transitioning */ = {
			isa = PBXGroup;
			children = (
				44125DBE22538E6D003C1762 /* REATransitionManager.h */,
				44125DBF22538E6D003C1762 /* REATransitionManager.m */,
				44125DC122538F68003C1762 /* REATransition.h */,
				44125DC222538F68003C1762 /* REATransition.m */,
				44125DC62253906A003C1762 /* REAAllTransitions.h */,
				44125DC72253906A003C1762 /* REAAllTransitions.m */,
				44125DC922539177003C1762 /* REATransitionAnimation.h */,
				44125DCA22539177003C1762 /* REATransitionAnimation.m */,
				44125DCC2253A038003C1762 /* REATransitionValues.h */,
				44125DCD2253A038003C1762 /* REATransitionValues.m */,
				44125DCF2253A3C0003C1762 /* RCTConvert+REATransition.h */,
				44125DD02253A3C0003C1762 /* RCTConvert+REATransition.m */,
			);
			path = Transitioning;
			sourceTree = "<group>";
		};
		58B511D21A9E6C8500147676 = {
			isa = PBXGroup;
			children = (
				4DA383A8226FA6A400582919 /* REAUtils.h */,
				44125C76224FBE0B003C1762 /* Transitioning */,
				440FEC0C209062F100EEC73A /* Nodes */,
				440FEC0A209062F100EEC73A /* REAModule.h */,
				440FEC2A209062F100EEC73A /* REAModule.m */,
				440FEC01209062F100EEC73A /* REANodesManager.h */,
				440FEC2B209062F100EEC73A /* REANodesManager.m */,
				134814211AA4EA7D00B7C361 /* Products */,
				FDBB176E229BF04900D1E455 /* libRNReanimated-tvOS.a */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		58B511DA1A9E6C8500147676 /* RNReanimated */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "RNReanimated" */;
			buildPhases = (
				58B511D71A9E6C8500147676 /* Sources */,
				58B511D81A9E6C8500147676 /* Frameworks */,
				58B511D91A9E6C8500147676 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNReanimated;
			productName = RCTDataManager;
			productReference = 134814201AA4EA6300B7C361 /* libRNReanimated.a */;
			productType = "com.apple.product-type.library.static";
		};
		FDBB176D229BF04900D1E455 /* RNReanimated-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FDBB1774229BF04900D1E455 /* Build configuration list for PBXNativeTarget "RNReanimated-tvOS" */;
			buildPhases = (
				FDBB176A229BF04900D1E455 /* Sources */,
				FDBB176B229BF04900D1E455 /* Frameworks */,
				FDBB176C229BF04900D1E455 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "RNReanimated-tvOS";
			productName = "RNReanimated-tvOS";
			productReference = FDBB176E229BF04900D1E455 /* libRNReanimated-tvOS.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		58B511D31A9E6C8500147676 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0920;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					58B511DA1A9E6C8500147676 = {
						CreatedOnToolsVersion = 6.1.1;
					};
					FDBB176D229BF04900D1E455 = {
						CreatedOnToolsVersion = 10.2.1;
						DevelopmentTeam = NZDV3AFAEG;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "RNReanimated" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 58B511D21A9E6C8500147676;
			productRefGroup = 58B511D21A9E6C8500147676;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				58B511DA1A9E6C8500147676 /* RNReanimated */,
				FDBB176D229BF04900D1E455 /* RNReanimated-tvOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		58B511D71A9E6C8500147676 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				440FEC2E209062F100EEC73A /* READebugNode.m in Sources */,
				440FEC37209062F100EEC73A /* REASetNode.m in Sources */,
				440FEC3A209062F100EEC73A /* REAModule.m in Sources */,
				44125DCE2253A038003C1762 /* REATransitionValues.m in Sources */,
				44125DC82253906B003C1762 /* REAAllTransitions.m in Sources */,
				A12DA6B722EC228D00E8271A /* REAParamNode.m in Sources */,
				44125DC022538E6D003C1762 /* REATransitionManager.m in Sources */,
				440FEC39209062F100EEC73A /* REABlockNode.m in Sources */,
				A12DA6C022EC22E400E8271A /* REACallFuncNode.m in Sources */,
				440FEC33209062F100EEC73A /* REABezierNode.m in Sources */,
				44125DC322538F68003C1762 /* REATransition.m in Sources */,
				440FEC2D209062F100EEC73A /* REAStyleNode.m in Sources */,
				440FEC31209062F100EEC73A /* REACondNode.m in Sources */,
				44125DD12253A3C0003C1762 /* RCTConvert+REATransition.m in Sources */,
				440FEC2C209062F100EEC73A /* REAPropsNode.m in Sources */,
				A12DA6BE22EC22E400E8271A /* REAFunctionNode.m in Sources */,
				440FEC3B209062F100EEC73A /* REANodesManager.m in Sources */,
				440FEC36209062F100EEC73A /* REAEventNode.m in Sources */,
				44125DCB22539177003C1762 /* REATransitionAnimation.m in Sources */,
				66240C6920C68DEA00648F55 /* REAAlwaysNode.m in Sources */,
				440FEC2F209062F100EEC73A /* REAClockNodes.m in Sources */,
				660A44292119B821006BFD5E /* REAConcatNode.m in Sources */,
				440FEC38209062F100EEC73A /* REAOperatorNode.m in Sources */,
				440FEC34209062F100EEC73A /* REANode.m in Sources */,
				440FEC30209062F100EEC73A /* REAValueNode.m in Sources */,
				440FEC32209062F100EEC73A /* REAJSCallNode.m in Sources */,
				440FEC35209062F100EEC73A /* REATransformNode.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FDBB176A229BF04900D1E455 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FDE6D93C229BF70F007F6716 /* REAOperatorNode.m in Sources */,
				FDE6D93F229BF70F007F6716 /* REAClockNodes.m in Sources */,
				FDE6D936229BF70F007F6716 /* REAValueNode.m in Sources */,
				FDE6D941229BF70F007F6716 /* REABezierNode.m in Sources */,
				FDBB177B229BF0E700D1E455 /* REAAllTransitions.m in Sources */,
				A12DA6B822EC228D00E8271A /* REAParamNode.m in Sources */,
				FDE6D93D229BF70F007F6716 /* REASetNode.m in Sources */,
				FDE6D93A229BF70F007F6716 /* REABlockNode.m in Sources */,
				A12DA6C122EC22E400E8271A /* REACallFuncNode.m in Sources */,
				FDE6D939229BF70F007F6716 /* REAPropsNode.m in Sources */,
				FDBB1778229BF0DE00D1E455 /* REANodesManager.m in Sources */,
				FDE6D937229BF70F007F6716 /* REAStyleNode.m in Sources */,
				FDE6D940229BF70F007F6716 /* REAJSCallNode.m in Sources */,
				FDE6D935229BF70F007F6716 /* REANode.m in Sources */,
				FDE6D93B229BF70F007F6716 /* REACondNode.m in Sources */,
				A12DA6BF22EC22E400E8271A /* REAFunctionNode.m in Sources */,
				FDBB177C229BF0E700D1E455 /* REATransitionAnimation.m in Sources */,
				FDE6D934229BF70F007F6716 /* REAAlwaysNode.m in Sources */,
				FDE6D933229BF70F007F6716 /* REAConcatNode.m in Sources */,
				FDBB177D229BF0E700D1E455 /* REATransitionValues.m in Sources */,
				FDE6D942229BF70F007F6716 /* REAEventNode.m in Sources */,
				FDE6D93E229BF70F007F6716 /* READebugNode.m in Sources */,
				FDE6D938229BF70F007F6716 /* REATransformNode.m in Sources */,
				FDBB177A229BF0E700D1E455 /* REATransition.m in Sources */,
				FDBB177E229BF0E700D1E455 /* RCTConvert+REATransition.m in Sources */,
				FDBB1777229BF0DE00D1E455 /* REAModule.m in Sources */,
				FDBB1779229BF0E700D1E455 /* REATransitionManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		58B511ED1A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		58B511EE1A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"NDEBUG=1",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		58B511F01A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNReanimated;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		58B511F11A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNReanimated;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		FDBB1775229BF04900D1E455 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = NZDV3AFAEG;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 12.2;
			};
			name = Debug;
		};
		FDBB1776229BF04900D1E455 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = NZDV3AFAEG;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				MTL_FAST_MATH = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 12.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "RNReanimated" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511ED1A9E6C8500147676 /* Debug */,
				58B511EE1A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "RNReanimated" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511F01A9E6C8500147676 /* Debug */,
				58B511F11A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FDBB1774229BF04900D1E455 /* Build configuration list for PBXNativeTarget "RNReanimated-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FDBB1775229BF04900D1E455 /* Debug */,
				FDBB1776229BF04900D1E455 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 58B511D31A9E6C8500147676 /* Project object */;
}
