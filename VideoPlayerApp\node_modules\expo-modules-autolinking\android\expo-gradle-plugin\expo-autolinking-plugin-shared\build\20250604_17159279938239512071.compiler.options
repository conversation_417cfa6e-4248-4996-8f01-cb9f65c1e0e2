"-Xallow-no-source-files" "-classpath" "C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-serialization-json-jvm\\1.6.3\\2241746853abf04073e3ab0dcd9e6729d363b313\\kotlinx-serialization-json-jvm-1.6.3.jar;C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-serialization-core-jvm\\1.6.3\\6b6c17d0312ba7192893adea9d52959941d0119b\\kotlinx-serialization-core-jvm-1.6.3.jar;C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.24\\9928532f12c66ad816a625b3f9984f8368ca6d2b\\kotlin-stdlib-1.9.24.jar;C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar" "-d" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\build\\classes\\kotlin\\main" "-jdk-home" "C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot" "-jvm-target" "11" "-module-name" "expo-autolinking-plugin-shared" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-compiler-embeddable\\1.9.24\\1e6b33f8a691420c1265349809192b66b7e56e8b\\kotlin-scripting-compiler-embeddable-1.9.24.jar,C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-serialization-compiler-plugin-embeddable\\1.9.24\\2e08b783b33eb7f83f6f24e03b35184ba4dded4c\\kotlin-serialization-compiler-plugin-embeddable-1.9.24.jar,C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-compiler-impl-embeddable\\1.9.24\\e1c67ba2d4663fbb142770f473230acc84ffdc07\\kotlin-scripting-compiler-impl-embeddable-1.9.24.jar,C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-jvm\\1.9.24\\f179cc31fb89102c0e229c23b7d852d9840e13c7\\kotlin-scripting-jvm-1.9.24.jar,C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-scripting-common\\1.9.24\\48e672815d9b6bb7fcebb8c1bd2de0c449e4fb16\\kotlin-scripting-common-1.9.24.jar,C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\1.9.24\\9928532f12c66ad816a625b3f9984f8368ca6d2b\\kotlin-stdlib-1.9.24.jar,C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar,C:\\Users\\\<USER>\u0648\u0642 \u0627\u0644\u0643\u0645\u0628\u064A\u0648\u062A\u0631\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-script-runtime\\1.9.24\\96771497da90fbc5af1c90fce148db2595a62502\\kotlin-script-runtime-1.9.24.jar" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\src\\main\\kotlin\\expo\\modules\\plugin\\AutolinkigCommandBuilder.kt" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\src\\main\\kotlin\\expo\\modules\\plugin\\configuration\\ExpoAutolinkingConfig.kt" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\src\\main\\kotlin\\expo\\modules\\plugin\\configuration\\MavenCredentials.kt" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\src\\main\\kotlin\\expo\\modules\\plugin\\ExpoGradleExtension.kt" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\src\\main\\kotlin\\expo\\modules\\plugin\\Os.kt" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\src\\main\\kotlin\\expo\\modules\\plugin\\text\\Colors.kt" "D:\\horizons-export-28e00aaf-ff1b-4662-b58f-491e5e68eca4\\VideoPlayerApp\\node_modules\\expo-modules-autolinking\\android\\expo-gradle-plugin\\expo-autolinking-plugin-shared\\src\\main\\kotlin\\expo\\modules\\plugin\\text\\Emojis.kt"