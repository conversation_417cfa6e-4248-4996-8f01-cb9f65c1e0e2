{"version": 3, "names": ["ReanimatedError", "NEWTON_ITERATIONS", "NEWTON_MIN_SLOPE", "SUBDIVISION_PRECISION", "SUBDIVISION_MAX_ITERATIONS", "kSplineTableSize", "kSampleStepSize", "A", "aA1", "aA2", "B", "C", "calcBezier", "aT", "getSlope", "binarySubdivide", "aX", "aA", "aB", "mX1", "mX2", "currentX", "currentT", "i", "Math", "abs", "newtonRaphsonIterate", "aGuessT", "currentSlope", "<PERSON><PERSON>", "mY1", "mY2", "LinearEasing", "x", "sampleValues", "Array", "getTForX", "intervalStart", "currentSample", "lastSample", "dist", "guessForT", "initialSlope", "BezierEasing"], "sourceRoot": "../../src", "sources": ["Bezier.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,aAAU;;AAE1C;AACA;AACA;AACA;;AAEA;;AAEA,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,gBAAgB,GAAG,KAAK;AAC9B,MAAMC,qBAAqB,GAAG,SAAS;AACvC,MAAMC,0BAA0B,GAAG,EAAE;AAErC,MAAMC,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,eAAe,GAAG,GAAG,IAAID,gBAAgB,GAAG,GAAG,CAAC;AAEtD,SAASE,CAACA,CAACC,GAAW,EAAEC,GAAW,EAAU;EAC3C,SAAS;;EACT,OAAO,GAAG,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AACpC;AACA,SAASE,CAACA,CAACF,GAAW,EAAEC,GAAW,EAAU;EAC3C,SAAS;;EACT,OAAO,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGD,GAAG;AAC9B;AACA,SAASG,CAACA,CAACH,GAAW,EAAE;EACtB,SAAS;;EACT,OAAO,GAAG,GAAGA,GAAG;AAClB;;AAEA;AACA,SAASI,UAAUA,CAACC,EAAU,EAAEL,GAAW,EAAEC,GAAW,EAAU;EAChE,SAAS;;EACT,OAAO,CAAC,CAACF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,IAAII,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC,IAAIK,EAAE;AAC9D;;AAEA;AACA,SAASC,QAAQA,CAACD,EAAU,EAAEL,GAAW,EAAEC,GAAW,EAAU;EAC9D,SAAS;;EACT,OAAO,GAAG,GAAGF,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGA,EAAE,GAAG,GAAG,GAAGH,CAAC,CAACF,GAAG,EAAEC,GAAG,CAAC,GAAGI,EAAE,GAAGF,CAAC,CAACH,GAAG,CAAC;AACtE;AAEA,SAASO,eAAeA,CACtBC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,GAAW,EACXC,GAAW,EACH;EACR,SAAS;;EACT,IAAIC,QAAQ;EACZ,IAAIC,QAAQ;EACZ,IAAIC,CAAC,GAAG,CAAC;EACT,GAAG;IACDD,QAAQ,GAAGL,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAI,GAAG;IAC/BI,QAAQ,GAAGT,UAAU,CAACU,QAAQ,EAAEH,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IAC9C,IAAIK,QAAQ,GAAG,GAAG,EAAE;MAClBH,EAAE,GAAGI,QAAQ;IACf,CAAC,MAAM;MACLL,EAAE,GAAGK,QAAQ;IACf;EACF,CAAC,QACCE,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAC,GAAGlB,qBAAqB,IAC1C,EAAEoB,CAAC,GAAGnB,0BAA0B;EAElC,OAAOkB,QAAQ;AACjB;AAEA,SAASI,oBAAoBA,CAC3BV,EAAU,EACVW,OAAe,EACfR,GAAW,EACXC,GAAW,EACH;EACR,SAAS;;EACT,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,iBAAiB,EAAE,EAAEsB,CAAC,EAAE;IAC1C,MAAMK,YAAY,GAAGd,QAAQ,CAACa,OAAO,EAAER,GAAG,EAAEC,GAAG,CAAC;IAChD,IAAIQ,YAAY,KAAK,GAAG,EAAE;MACxB,OAAOD,OAAO;IAChB;IACA,MAAMN,QAAQ,GAAGT,UAAU,CAACe,OAAO,EAAER,GAAG,EAAEC,GAAG,CAAC,GAAGJ,EAAE;IACnDW,OAAO,IAAIN,QAAQ,GAAGO,YAAY;EACpC;EACA,OAAOD,OAAO;AAChB;AAEA,OAAO,SAASE,MAAMA,CACpBV,GAAW,EACXW,GAAW,EACXV,GAAW,EACXW,GAAW,EACY;EACvB,SAAS;;EAET,SAASC,YAAYA,CAACC,CAAS,EAAU;IACvC,SAAS;;IACT,OAAOA,CAAC;EACV;EAEA,IAAI,EAAEd,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIC,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC,EAAE;IACnD,MAAM,IAAIpB,eAAe,CAAC,0CAA0C,CAAC;EACvE;EAEA,IAAImB,GAAG,KAAKW,GAAG,IAAIV,GAAG,KAAKW,GAAG,EAAE;IAC9B,OAAOC,YAAY;EACrB;EAEA,MAAME,YAAY,GAAG,IAAIC,KAAK,CAAC9B,gBAAgB,CAAC;;EAEhD;EACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,gBAAgB,EAAE,EAAEkB,CAAC,EAAE;IACzCW,YAAY,CAACX,CAAC,CAAC,GAAGX,UAAU,CAACW,CAAC,GAAGjB,eAAe,EAAEa,GAAG,EAAEC,GAAG,CAAC;EAC7D;EAEA,SAASgB,QAAQA,CAACpB,EAAU,EAAU;IACpC,SAAS;;IACT,IAAIqB,aAAa,GAAG,GAAG;IACvB,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,UAAU,GAAGlC,gBAAgB,GAAG,CAAC;IAEvC,OAEEiC,aAAa,KAAKC,UAAU,IAAIL,YAAY,CAACI,aAAa,CAAC,IAAItB,EAAE,EACjE,EAAEsB,aAAa,EACf;MACAD,aAAa,IAAI/B,eAAe;IAClC;IACA,EAAEgC,aAAa;;IAEf;IACA,MAAME,IAAI,GACR,CAACxB,EAAE,GAAGkB,YAAY,CAACI,aAAa,CAAC,KAChCJ,YAAY,CAACI,aAAa,GAAG,CAAC,CAAC,GAAGJ,YAAY,CAACI,aAAa,CAAC,CAAC;IACjE,MAAMG,SAAS,GAAGJ,aAAa,GAAGG,IAAI,GAAGlC,eAAe;IAExD,MAAMoC,YAAY,GAAG5B,QAAQ,CAAC2B,SAAS,EAAEtB,GAAG,EAAEC,GAAG,CAAC;IAClD,IAAIsB,YAAY,IAAIxC,gBAAgB,EAAE;MACpC,OAAOwB,oBAAoB,CAACV,EAAE,EAAEyB,SAAS,EAAEtB,GAAG,EAAEC,GAAG,CAAC;IACtD,CAAC,MAAM,IAAIsB,YAAY,KAAK,GAAG,EAAE;MAC/B,OAAOD,SAAS;IAClB,CAAC,MAAM;MACL,OAAO1B,eAAe,CACpBC,EAAE,EACFqB,aAAa,EACbA,aAAa,GAAG/B,eAAe,EAC/Ba,GAAG,EACHC,GACF,CAAC;IACH;EACF;EAEA,OAAO,SAASuB,YAAYA,CAACV,CAAC,EAAE;IAC9B,SAAS;;IACT,IAAId,GAAG,KAAKW,GAAG,IAAIV,GAAG,KAAKW,GAAG,EAAE;MAC9B,OAAOE,CAAC,CAAC,CAAC;IACZ;IACA;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV;IACA,OAAOrB,UAAU,CAACwB,QAAQ,CAACH,CAAC,CAAC,EAAEH,GAAG,EAAEC,GAAG,CAAC;EAC1C,CAAC;AACH", "ignoreList": []}