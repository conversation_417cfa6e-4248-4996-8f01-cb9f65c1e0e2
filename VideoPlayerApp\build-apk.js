const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية بناء APK...');

// التحقق من وجود مجلد android
const androidPath = path.join(__dirname, 'android');
if (!fs.existsSync(androidPath)) {
  console.log('📁 إنشاء مجلد Android...');
  try {
    execSync('npx expo prebuild --platform android', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ خطأ في إنشاء مجلد Android:', error.message);
    process.exit(1);
  }
}

// بناء APK
console.log('🔨 بناء APK...');
try {
  // الانتقال إلى مجلد android وبناء APK
  process.chdir(path.join(__dirname, 'android'));
  
  // تنظيف البناء السابق
  console.log('🧹 تنظيف البناء السابق...');
  execSync('gradlew.bat clean', { stdio: 'inherit' });

  // بناء APK للإصدار
  console.log('📦 بناء APK للإصدار...');
  execSync('gradlew.bat assembleRelease', { stdio: 'inherit' });
  
  // البحث عن ملف APK
  const apkPath = path.join(__dirname, 'android', 'app', 'build', 'outputs', 'apk', 'release', 'app-release.apk');
  
  if (fs.existsSync(apkPath)) {
    console.log('✅ تم بناء APK بنجاح!');
    console.log('📍 مسار الملف:', apkPath);
    
    // نسخ APK إلى المجلد الرئيسي
    const targetPath = path.join(__dirname, 'advanced-video-player.apk');
    fs.copyFileSync(apkPath, targetPath);
    console.log('📋 تم نسخ APK إلى:', targetPath);
    
    // عرض معلومات الملف
    const stats = fs.statSync(targetPath);
    console.log('📊 حجم الملف:', (stats.size / (1024 * 1024)).toFixed(2), 'MB');
    
  } else {
    console.error('❌ لم يتم العثور على ملف APK');
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ خطأ في بناء APK:', error.message);
  
  // محاولة بناء APK للتطوير كبديل
  console.log('🔄 محاولة بناء APK للتطوير...');
  try {
    execSync('gradlew.bat assembleDebug', { stdio: 'inherit' });
    
    const debugApkPath = path.join(__dirname, 'android', 'app', 'build', 'outputs', 'apk', 'debug', 'app-debug.apk');
    
    if (fs.existsSync(debugApkPath)) {
      console.log('✅ تم بناء APK للتطوير بنجاح!');
      
      // نسخ APK إلى المجلد الرئيسي
      const targetPath = path.join(__dirname, 'advanced-video-player-debug.apk');
      fs.copyFileSync(debugApkPath, targetPath);
      console.log('📋 تم نسخ APK إلى:', targetPath);
      
      // عرض معلومات الملف
      const stats = fs.statSync(targetPath);
      console.log('📊 حجم الملف:', (stats.size / (1024 * 1024)).toFixed(2), 'MB');
      
    } else {
      console.error('❌ فشل في بناء APK');
      process.exit(1);
    }
    
  } catch (debugError) {
    console.error('❌ فشل في بناء APK للتطوير:', debugError.message);
    process.exit(1);
  }
}

console.log('🎉 تم الانتهاء من عملية البناء!');
console.log('');
console.log('📱 لتثبيت APK على جهاز الأندرويد:');
console.log('1. انقل ملف APK إلى جهاز الأندرويد');
console.log('2. فعل "تثبيت من مصادر غير معروفة" في الإعدادات');
console.log('3. اضغط على ملف APK لتثبيته');
