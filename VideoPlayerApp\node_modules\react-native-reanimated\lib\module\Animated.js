'use strict';

export { ReanimatedFlatList as FlatList } from "./component/FlatList.js";
export { AnimatedImage as Image } from "./component/Image.js";
export { AnimatedScrollView as ScrollView } from "./component/ScrollView.js";
export { AnimatedText as Text } from "./component/Text.js";
export { AnimatedView as View } from "./component/View.js";
export { addWhitelistedNativeProps, addWhitelistedUIProps } from "./ConfigHelper.js";
export { createAnimatedComponent } from "./createAnimatedComponent/index.js";
/**
 * @deprecated Please import `Extrapolate` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `SharedValue` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `DerivedValue` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `Adaptable` directly from `react-native-reanimated`
 *   instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `TransformStyleTypes` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `AdaptTransforms` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `AnimatedTransform` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `AnimateStyle` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `StylesOrDefault` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `AnimateProps` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `EasingFunction` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `AnimatedScrollViewProps` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */

/**
 * @deprecated Please import `FlatListPropsWithLayout` directly from
 *   `react-native-reanimated` instead of `Animated` namespace.
 */
//# sourceMappingURL=Animated.js.map