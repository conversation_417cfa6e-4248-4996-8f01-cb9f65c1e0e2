{"version": 3, "names": ["registerReanimatedError", "reportFatalErrorOnJS", "DEFAULT_LOGGER_CONFIG", "logToLogBoxAndConsole", "registerLoggerConfig", "replaceLoggerImplementation", "mockedRequestAnimationFrame", "isChromeDebugger", "isJest", "isWeb", "shouldBeUseWeb", "callMicrotasks", "executeOnUIRuntimeSync", "runOnJS", "runOnUIImmediately", "setupMicrotasks", "IS_JEST", "SHOULD_BE_USE_WEB", "IS_CHROME_DEBUGGER", "overrideLogFunctionImplementation", "data", "global", "_WORKLET", "_log", "console", "log", "_getAnimationTimestamp", "performance", "now", "callGuardDEV", "fn", "args", "e", "__E<PERSON><PERSON><PERSON><PERSON>s", "reportFatalError", "setupCallGuard", "__callGuardDEV", "error", "message", "stack", "createMemorySafeCapturableConsole", "consoleCopy", "Object", "fromEntries", "entries", "map", "methodName", "method", "methodWrapper", "name", "defineProperty", "value", "writable", "capturableConsole", "setupConsole", "assert", "debug", "warn", "info", "setupRequestAnimationFrame", "nativeRequestAnimationFrame", "requestAnimationFrame", "animationFrameCallbacks", "flushRequested", "__flushAnimationFrame", "frameTimestamp", "currentCallbacks", "for<PERSON>ach", "f", "callback", "push", "timestamp", "__frameTimestamp", "undefined", "initializeUIRuntime", "ReanimatedModule", "Error", "globalThis"], "sourceRoot": "../../src", "sources": ["initializers.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,uBAAuB,EAAEC,oBAAoB,QAAQ,aAAU;AACxE,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,oBAAoB,EACpBC,2BAA2B,QACtB,mBAAU;AACjB,SAASC,2BAA2B,QAAQ,kCAA+B;AAC3E,SACEC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,cAAc,QACT,sBAAmB;AAC1B,SACEC,cAAc,EACdC,sBAAsB,EACtBC,OAAO,EACPC,kBAAkB,EAClBC,eAAe,QACV,cAAW;AAElB,MAAMC,OAAO,GAAGR,MAAM,CAAC,CAAC;AACxB,MAAMS,iBAAiB,GAAGP,cAAc,CAAC,CAAC;AAC1C,MAAMQ,kBAAkB,GAAGX,gBAAgB,CAAC,CAAC;;AAE7C;AACA;AACA;AACA,SAASY,iCAAiCA,CAAA,EAAG;EAC3C,SAAS;;EACTd,2BAA2B,CAAEe,IAAI,IAAK;IACpC,SAAS;;IACTP,OAAO,CAACV,qBAAqB,CAAC,CAACiB,IAAI,CAAC;EACtC,CAAC,CAAC;AACJ;;AAEA;AACA;AACAhB,oBAAoB,CAACF,qBAAqB,CAAC;AAC3CiB,iCAAiC,CAAC,CAAC;;AAEnC;AACA,IAAIF,iBAAiB,EAAE;EACrBI,MAAM,CAACC,QAAQ,GAAG,KAAK;EACvBD,MAAM,CAACE,IAAI,GAAGC,OAAO,CAACC,GAAG;EACzBJ,MAAM,CAACK,sBAAsB,GAAG,MAAMC,WAAW,CAACC,GAAG,CAAC,CAAC;AACzD,CAAC,MAAM;EACL;EACA;EACA;EACAhB,sBAAsB,CAACZ,uBAAuB,CAAC,CAAC,CAAC;EACjDY,sBAAsB,CAACR,oBAAoB,CAAC,CAACF,qBAAqB,CAAC;EACnEU,sBAAsB,CAACO,iCAAiC,CAAC,CAAC,CAAC;AAC7D;;AAEA;AACA,OAAO,SAASU,YAAYA,CAC1BC,EAAkC,EAClC,GAAGC,IAAU,EACO;EACpB,SAAS;;EACT,IAAI;IACF,OAAOD,EAAE,CAAC,GAAGC,IAAI,CAAC;EACpB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAIX,MAAM,CAACY,YAAY,EAAE;MACvBZ,MAAM,CAACY,YAAY,CAACC,gBAAgB,CAACF,CAAU,CAAC;IAClD,CAAC,MAAM;MACL,MAAMA,CAAC;IACT;EACF;AACF;AAEA,OAAO,SAASG,cAAcA,CAAA,EAAG;EAC/B,SAAS;;EACTd,MAAM,CAACe,cAAc,GAAGP,YAAY;EACpCR,MAAM,CAACY,YAAY,GAAG;IACpBC,gBAAgB,EAAGG,KAAY,IAAK;MAClCxB,OAAO,CAACZ,oBAAoB,CAAC,CAAC;QAC5BqC,OAAO,EAAED,KAAK,CAACC,OAAO;QACtBC,KAAK,EAAEF,KAAK,CAACE;MACf,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAAA,EAAmB;EAC3D,MAAMC,WAAW,GAAGC,MAAM,CAACC,WAAW,CACpCD,MAAM,CAACE,OAAO,CAACpB,OAAO,CAAC,CAACqB,GAAG,CAAC,CAAC,CAACC,UAAU,EAAEC,MAAM,CAAC,KAAK;IACpD,MAAMC,aAAa,GAAG,SAASA,aAAaA,CAAC,GAAGjB,IAAe,EAAE;MAC/D,OAAOgB,MAAM,CAAC,GAAGhB,IAAI,CAAC;IACxB,CAAC;IACD,IAAIgB,MAAM,CAACE,IAAI,EAAE;MACf;AACR;AACA;AACA;AACA;AACA;AACA;MACQP,MAAM,CAACQ,cAAc,CAACF,aAAa,EAAE,MAAM,EAAE;QAC3CG,KAAK,EAAEJ,MAAM,CAACE,IAAI;QAClBG,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,CAACN,UAAU,EAAEE,aAAa,CAAC;EACpC,CAAC,CACH,CAAC;EAED,OAAOP,WAAW;AACpB;;AAEA;AACA;AACA,MAAMY,iBAAiB,GAAGb,iCAAiC,CAAC,CAAC;AAE7D,OAAO,SAASc,YAAYA,CAAA,EAAG;EAC7B,SAAS;;EACT,IAAI,CAACpC,kBAAkB,EAAE;IACvB;IACAG,MAAM,CAACG,OAAO,GAAG;MACf;MACA+B,MAAM,EAAE1C,OAAO,CAACwC,iBAAiB,CAACE,MAAM,CAAC;MACzCC,KAAK,EAAE3C,OAAO,CAACwC,iBAAiB,CAACG,KAAK,CAAC;MACvC/B,GAAG,EAAEZ,OAAO,CAACwC,iBAAiB,CAAC5B,GAAG,CAAC;MACnCgC,IAAI,EAAE5C,OAAO,CAACwC,iBAAiB,CAACI,IAAI,CAAC;MACrCpB,KAAK,EAAExB,OAAO,CAACwC,iBAAiB,CAAChB,KAAK,CAAC;MACvCqB,IAAI,EAAE7C,OAAO,CAACwC,iBAAiB,CAACK,IAAI;MACpC;IACF,CAAC;EACH;AACF;AAEA,SAASC,0BAA0BA,CAAA,EAAG;EACpC,SAAS;;EAET;EACA;EACA,MAAMC,2BAA2B,GAAGvC,MAAM,CAACwC,qBAAqB;EAEhE,IAAIC,uBAA2D,GAAG,EAAE;EACpE,IAAIC,cAAc,GAAG,KAAK;EAE1B1C,MAAM,CAAC2C,qBAAqB,GAAIC,cAAsB,IAAK;IACzD,MAAMC,gBAAgB,GAAGJ,uBAAuB;IAChDA,uBAAuB,GAAG,EAAE;IAC5BI,gBAAgB,CAACC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACH,cAAc,CAAC,CAAC;IAClDtD,cAAc,CAAC,CAAC;EAClB,CAAC;EAEDU,MAAM,CAACwC,qBAAqB,GAC1BQ,QAAqC,IAC1B;IACXP,uBAAuB,CAACQ,IAAI,CAACD,QAAQ,CAAC;IACtC,IAAI,CAACN,cAAc,EAAE;MACnBA,cAAc,GAAG,IAAI;MACrBH,2BAA2B,CAAEW,SAAS,IAAK;QACzCR,cAAc,GAAG,KAAK;QACtB1C,MAAM,CAACmD,gBAAgB,GAAGD,SAAS;QACnClD,MAAM,CAAC2C,qBAAqB,CAACO,SAAS,CAAC;QACvClD,MAAM,CAACmD,gBAAgB,GAAGC,SAAS;MACrC,CAAC,CAAC;IACJ;IACA;IACA;IACA;IACA;IACA,OAAO,CAAC,CAAC;EACX,CAAC;AACH;AAEA,OAAO,SAASC,mBAAmBA,CAACC,gBAAmC,EAAE;EACvE,IAAIlE,KAAK,CAAC,CAAC,EAAE;IACX;EACF;EACA,IAAI,CAACkE,gBAAgB,EAAE;IACrB;IACA,MAAM,IAAIC,KAAK,CACb,iGACF,CAAC;EACH;EACA,IAAI5D,OAAO,EAAE;IACX;IACA;IACA;IACA;IACA;IACA;IACA6D,UAAU,CAAChB,qBAAqB,GAAGvD,2BAA2B;EAChE;EAEAQ,kBAAkB,CAAC,MAAM;IACvB,SAAS;;IACTqB,cAAc,CAAC,CAAC;IAChBmB,YAAY,CAAC,CAAC;IACd,IAAI,CAACrC,iBAAiB,EAAE;MACtBF,eAAe,CAAC,CAAC;MACjB4C,0BAA0B,CAAC,CAAC;IAC9B;EACF,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}