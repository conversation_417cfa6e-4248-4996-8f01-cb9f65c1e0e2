{"version": 3, "names": ["<PERSON><PERSON>", "linear", "t", "ease", "quad", "cubic", "poly", "n", "Math", "pow", "sin", "cos", "PI", "circle", "sqrt", "exp", "elastic", "bounciness", "p", "back", "s", "bounce", "t2", "bezier", "x1", "y1", "x2", "y2", "factory", "bezierFn", "in_", "easing", "out", "inOut", "steps", "roundToNextStep", "value", "min", "max", "ceil", "floor", "EasingObject", "in", "EasingNameSymbol", "Symbol", "easingName", "Object", "entries", "defineProperty", "configurable", "enumerable", "writable", "Easing"], "sourceRoot": "../../src", "sources": ["Easing.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,MAAM,QAAQ,aAAU;;AAGjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,CAAS,EAAU;EACjC,SAAS;;EACT,OAAOA,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACD,CAAS,EAAU;EAC/B,SAAS;;EACT,OAAOF,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACE,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,IAAIA,CAACF,CAAS,EAAU;EAC/B,SAAS;;EACT,OAAOA,CAAC,GAAGA,CAAC;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,KAAKA,CAACH,CAAS,EAAU;EAChC,SAAS;;EACT,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,IAAIA,CAACC,CAAS,EAAkB;EACvC,SAAS;;EACT,OAAQL,CAAC,IAAK;IACZ,SAAS;;IACT,OAAOM,IAAI,CAACC,GAAG,CAACP,CAAC,EAAEK,CAAC,CAAC;EACvB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,GAAGA,CAACR,CAAS,EAAU;EAC9B,SAAS;;EACT,OAAO,CAAC,GAAGM,IAAI,CAACG,GAAG,CAAET,CAAC,GAAGM,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACX,CAAS,EAAU;EACjC,SAAS;;EACT,OAAO,CAAC,GAAGM,IAAI,CAACM,IAAI,CAAC,CAAC,GAAGZ,CAAC,GAAGA,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASa,GAAGA,CAACb,CAAS,EAAU;EAC9B,SAAS;;EACT,OAAOM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIP,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,OAAOA,CAACC,UAAU,GAAG,CAAC,EAAkB;EAC/C,SAAS;;EACT,MAAMC,CAAC,GAAGD,UAAU,GAAGT,IAAI,CAACI,EAAE;EAC9B,OAAQV,CAAC,IAAK;IACZ,SAAS;;IACT,OAAO,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,GAAG,CAAET,CAAC,GAAGM,IAAI,CAACI,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGJ,IAAI,CAACG,GAAG,CAACT,CAAC,GAAGgB,CAAC,CAAC;EACvE,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,CAAC,GAAG,OAAO,EAAyB;EAChD,SAAS;;EACT,OAAQlB,CAAC,IAAK;IACZ,SAAS;;IACT,OAAOA,CAAC,GAAGA,CAAC,IAAI,CAACkB,CAAC,GAAG,CAAC,IAAIlB,CAAC,GAAGkB,CAAC,CAAC;EAClC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACnB,CAAS,EAAU;EACjC,SAAS;;EACT,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;IAChB,OAAO,MAAM,GAAGA,CAAC,GAAGA,CAAC;EACvB;EAEA,IAAIA,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;IAChB,MAAMoB,EAAE,GAAGpB,CAAC,GAAG,GAAG,GAAG,IAAI;IACzB,OAAO,MAAM,GAAGoB,EAAE,GAAGA,EAAE,GAAG,IAAI;EAChC;EAEA,IAAIpB,CAAC,GAAG,GAAG,GAAG,IAAI,EAAE;IAClB,MAAMoB,EAAE,GAAGpB,CAAC,GAAG,IAAI,GAAG,IAAI;IAC1B,OAAO,MAAM,GAAGoB,EAAE,GAAGA,EAAE,GAAG,MAAM;EAClC;EAEA,MAAMA,EAAE,GAAGpB,CAAC,GAAG,KAAK,GAAG,IAAI;EAC3B,OAAO,MAAM,GAAGoB,EAAE,GAAGA,EAAE,GAAG,QAAQ;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CACbC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACa;EACvB,SAAS;;EACT,OAAO;IACLC,OAAO,EAAEA,CAAA,KAAM;MACb,SAAS;;MACT,OAAO5B,MAAM,CAACwB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC/B;EACF,CAAC;AACH;AAEA,SAASE,QAAQA,CACfL,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACa;EACvB,SAAS;;EACT,OAAO3B,MAAM,CAACwB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC/B;;AAEA;AACA,SAASG,GAAGA,CAACC,MAAsB,EAAkB;EACnD,SAAS;;EACT,OAAOA,MAAM;AACf;;AAEA;AACA,SAASC,GAAGA,CAACD,MAAsB,EAAkB;EACnD,SAAS;;EACT,OAAQ7B,CAAC,IAAK;IACZ,SAAS;;IACT,OAAO,CAAC,GAAG6B,MAAM,CAAC,CAAC,GAAG7B,CAAC,CAAC;EAC1B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS+B,KAAKA,CAACF,MAAsB,EAAkB;EACrD,SAAS;;EACT,OAAQ7B,CAAC,IAAK;IACZ,SAAS;;IACT,IAAIA,CAAC,GAAG,GAAG,EAAE;MACX,OAAO6B,MAAM,CAAC7B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1B;IACA,OAAO,CAAC,GAAG6B,MAAM,CAAC,CAAC,CAAC,GAAG7B,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;EACpC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgC,KAAKA,CAAC3B,CAAC,GAAG,EAAE,EAAE4B,eAAe,GAAG,IAAI,EAAkB;EAC7D,SAAS;;EACT,OAAQjC,CAAC,IAAK;IACZ,SAAS;;IACT,MAAMkC,KAAK,GAAG5B,IAAI,CAAC6B,GAAG,CAAC7B,IAAI,CAAC8B,GAAG,CAACpC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGK,CAAC;IAC7C,IAAI4B,eAAe,EAAE;MACnB,OAAO3B,IAAI,CAAC+B,IAAI,CAACH,KAAK,CAAC,GAAG7B,CAAC;IAC7B;IACA,OAAOC,IAAI,CAACgC,KAAK,CAACJ,KAAK,CAAC,GAAG7B,CAAC;EAC9B,CAAC;AACH;AAEA,MAAMkC,YAAY,GAAG;EACnBxC,MAAM;EACNE,IAAI;EACJC,IAAI;EACJC,KAAK;EACLC,IAAI;EACJI,GAAG;EACHG,MAAM;EACNE,GAAG;EACHC,OAAO;EACPG,IAAI;EACJE,MAAM;EACNE,MAAM;EACNM,QAAQ;EACRK,KAAK;EACLQ,EAAE,EAAEZ,GAAG;EACPE,GAAG;EACHC;AACF,CAAC;AAED,OAAO,MAAMU,gBAAgB,GAAGC,MAAM,CAAC,YAAY,CAAC;AAEpD,KAAK,MAAM,CAACC,UAAU,EAAEd,MAAM,CAAC,IAAIe,MAAM,CAACC,OAAO,CAACN,YAAY,CAAC,EAAE;EAC/DK,MAAM,CAACE,cAAc,CAACjB,MAAM,EAAEY,gBAAgB,EAAE;IAC9CP,KAAK,EAAES,UAAU;IACjBI,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ;AAEA,OAAO,MAAMC,MAAM,GAAGX,YAAY", "ignoreList": []}